mod basic;

use basic::boot::{Bootstrap, BootstrapError};
use log::{error, info};

use thiserror::Error;
use tokio::process::Command;

/// 应用程序错误类型枚举
#[derive(Error, Debug)]
pub enum AppError {
    /// 启动错误
    #[error("启动错误: {0}")]
    Bootstrap(#[from] BootstrapError),

    /// IO 操作错误
    #[error("IO 错误: {0}")]
    Io(#[from] std::io::Error),

    /// 数字解析错误
    #[error("解析错误: {0}")]
    Parse(#[from] std::num::ParseIntError),

    /// 命令执行错误
    #[error("命令执行错误: {0}")]
    CommandExecution(String),

    /// 任务连接错误
    #[error("任务加入错误: {0}")]
    JoinError(#[from] tokio::task::JoinError),
}

/// 支持并行或串行执行配置文件中定义的命令
#[tokio::main]
async fn main() -> Result<(), AppError> {
    // 初始化应用
    let bootstrap = Bootstrap::new()?;

    // 从配置中获取所需的配置项
    let commands = &bootstrap.settings.commands; // 要执行的命令列表
    let parallel = bootstrap.settings.parallel; // 是否并行执行

    if parallel {
        // 并行执行模式: 同时执行所有命令
        let handles: Vec<_> = commands
            .iter()
            .map(|command| {
                let command = command.clone();
                tokio::spawn(async move {
                    let result = execute_command(&command).await;
                    log_command_result(&command, result).await;
                })
            })
            .collect();

        // 等待所有命令执行完成
        for handle in handles {
            handle
                .await
                .map_err(|e| AppError::CommandExecution(e.to_string()))?;
        }
    } else {
        // 串行执行模式: 按顺序执行命令
        for command in commands {
            let result = execute_command(command).await;
            log_command_result(command, result).await;
        }
    }

    Ok(())
}

/// 异步执行单个命令并捕获其输出（极简可靠模式）
async fn execute_command(command: &str) -> Result<String, AppError> {
    // 使用 output() 方法直接获取命令的完整输出，避免复杂的流处理
    let output = Command::new("sh")
        .arg("-c")
        .arg(command)
        .output()
        .await?;

    // 将输出转换为字符串
    let stdout = String::from_utf8_lossy(&output.stdout);
    let stderr = String::from_utf8_lossy(&output.stderr);
    let combined_output = format!("{}{}", stdout, stderr);

    // 检查命令执行状态
    if output.status.success() {
        Ok(combined_output)
    } else {
        Err(AppError::CommandExecution(format!(
            "命令执行失败 (退出码: {}): {}",
            output.status.code().unwrap_or(-1),
            combined_output
        )))
    }
}



/// 记录命令执行结果到日志
async fn log_command_result(command: &str, result: Result<String, AppError>) {
    match result {
        Ok(output) => {
            info!("命令: {}\n状态: 成功\n响应:\n{}", command, output);
        }
        Err(e) => error!("命令: {}\n错误: {}", command, e.to_string()),
    }
}
