mod basic;

use basic::boot::{Bootstrap, BootstrapError};
use log::{error, info};

use std::process::Stdio;
use thiserror::Error;
use tokio::io::{AsyncBufReadExt, BufReader};
use tokio::process::Command;
use tokio::sync::mpsc;

/// 应用程序错误类型枚举
#[derive(Error, Debug)]
pub enum AppError {
    /// 启动错误
    #[error("启动错误: {0}")]
    Bootstrap(#[from] BootstrapError),

    /// IO 操作错误
    #[error("IO 错误: {0}")]
    Io(#[from] std::io::Error),

    /// 数字解析错误
    #[error("解析错误: {0}")]
    Parse(#[from] std::num::ParseIntError),

    /// 命令执行错误
    #[error("命令执行错误: {0}")]
    CommandExecution(String),

    /// 任务连接错误
    #[error("任务加入错误: {0}")]
    JoinError(#[from] tokio::task::JoinError),
}

/// 支持并行或串行执行配置文件中定义的命令
#[tokio::main]
async fn main() -> Result<(), AppError> {
    // 初始化应用
    let bootstrap = Bootstrap::new()?;

    // 从配置中获取所需的配置项
    let commands = &bootstrap.settings.commands; // 要执行的命令列表
    let parallel = bootstrap.settings.parallel; // 是否并行执行

    if parallel {
        // 并行执行模式: 同时执行所有命令
        let handles: Vec<_> = commands
            .iter()
            .map(|command| {
                let command = command.clone();
                tokio::spawn(async move {
                    let result = execute_command(&command).await;
                    log_command_result(&command, result).await;
                })
            })
            .collect();

        // 等待所有命令执行完成
        for handle in handles {
            handle
                .await
                .map_err(|e| AppError::CommandExecution(e.to_string()))?;
        }
    } else {
        // 串行执行模式: 按顺序执行命令
        for command in commands {
            let result = execute_command(command).await;
            log_command_result(command, result).await;
        }
    }

    Ok(())
}

/// 异步执行单个命令并捕获其输出
async fn execute_command(command: &str) -> Result<String, AppError> {
    // 创建命令执行器, 配置标准输出和标准错误的捕获
    let mut cmd = Command::new("sh")
        .arg("-c")
        .arg(command)
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .spawn()?;

    // 获取标准输出和标准错误的句柄
    let stdout = cmd
        .stdout
        .take()
        .ok_or_else(|| AppError::CommandExecution("未能捕获标准输出".to_string()))?;
    let stderr = cmd
        .stderr
        .take()
        .ok_or_else(|| AppError::CommandExecution("未能捕获标准错误输出".to_string()))?;

    // 创建用于传输输出行的通道
    let (tx, mut rx) = mpsc::channel(100);

    // 创建标准输出和标准错误的读取器
    let stdout_reader = BufReader::new(stdout);
    let stderr_reader = BufReader::new(stderr);

    // 启动异步任务读取输出
    let stdout_task = tokio::spawn(read_lines(stdout_reader, tx.clone()));
    let stderr_task = tokio::spawn(read_lines(stderr_reader, tx.clone()));

    // 存储命令的输出
    let mut output = String::new();

    // 使用 tokio::join! 同时等待所有任务完成，避免死锁
    let (stdout_result, stderr_result, cmd_result) = tokio::join!(
        stdout_task,
        stderr_task,
        cmd.wait()
    );

    // 检查命令执行结果
    let status = cmd_result?;
    if !status.success() {
        return Err(AppError::CommandExecution(format!(
            "命令执行失败并返回了状态码: {}",
            status
        )));
    }

    // 检查输出读取任务的结果
    stdout_result.map_err(|e| AppError::CommandExecution(e.to_string()))??;
    stderr_result.map_err(|e| AppError::CommandExecution(e.to_string()))??;

    // 关闭发送端，确保接收端能正确结束
    drop(tx);

    // 收集所有输出行
    while let Some(line) = rx.recv().await {
        output.push_str(&line);
        output.push('\n');
    }

    Ok(output)
}

/// 异步读取输入流的每一行并发送到通道
async fn read_lines<R>(reader: BufReader<R>, tx: mpsc::Sender<String>) -> Result<(), AppError>
where
    R: tokio::io::AsyncRead + Unpin,
{
    let mut lines = reader.lines();
    while let Some(line) = lines.next_line().await? {
        tx.send(line)
            .await
            .map_err(|e| AppError::CommandExecution(e.to_string()))?;
    }
    Ok(())
}

/// 记录命令执行结果到日志
async fn log_command_result(command: &str, result: Result<String, AppError>) {
    match result {
        Ok(output) => {
            info!("命令: {}\n状态: 成功\n响应:\n{}", command, output);
        }
        Err(e) => error!("命令: {}\n错误: {}", command, e.to_string()),
    }
}
