use crate::basic::{
    config::conf::ConfigError,
    config::types::Settings,
    log::init_logger,
    utils::env_checker::{EnvCheckConfig, EnvCheckError, Env<PERSON><PERSON><PERSON>},
    utils::sysinfo::SysInfo,
};

use std::sync::Arc;
use thiserror::Error;

/// 启动错误
#[derive(Error, Debug)]
pub enum BootstrapError {
    #[error("日志初始化失败: {0}")]
    LogError(#[from] flexi_logger::FlexiLoggerError),

    #[error("配置加载失败: {0}")]
    ConfigError(#[from] ConfigError),

    #[error("环境检查失败: {0}")]
    EnvCheckError(#[from] EnvCheckError),

    #[error("其他错误: {0}")]
    Other(String),
}

/// 应用启动配置
#[derive(Debug)]
pub struct BootstrapConfig {
    /// 配置文件路径
    pub config_path: String,
    /// 是否显示系统信息
    pub show_sysinfo: bool,
    /// 环境检查配置
    pub env_check: EnvCheckConfig,
}

impl Default for BootstrapConfig {
    fn default() -> Self {
        Self {
            config_path: "config.toml".to_string(),
            show_sysinfo: true,
            env_check: EnvCheckConfig::default(),
        }
    }
}

/// 应用启动结果
pub struct Bootstrap {
    pub settings: Arc<Settings>,
}

impl Bootstrap {
    /// 使用默认配置创建启动实例
    pub fn new() -> Result<Self, BootstrapError> {
        let config = BootstrapConfig::default();

        // 执行环境检查
        let env_checker = EnvChecker::new(config.env_check);
        env_checker.check_all()?;

        // 初始化日志系统
        init_logger()?;

        // 根据配置决定是否打印系统信息
        if config.show_sysinfo {
            let sys_info = SysInfo::new();
            sys_info.print_system_info();
        }

        // 加载配置
        let settings = Settings::new(&config.config_path)?;

        Ok(Bootstrap {
            settings: Arc::new(settings),
        })
    }
}
