use log::{error, info};

use config::{Config, File};
use serde::de::DeserializeOwned;
use std::{fs, path::Path};
use thiserror::Error;
use validator::Validate;

/// 配置相关错误
#[derive(Error, Debug)]
pub enum ConfigError {
    /// 配置文件不存在
    #[error("配置文件 {0} 尚未发现")]
    FileNotFound(String),

    /// 配置文件为空
    #[error("配置文件 {0} 不可为空")]
    EmptyFile(String),

    /// 配置解析错误
    #[error("配置解析错误: {0}")]
    ParseError(#[from] config::ConfigError),

    /// 配置验证错误
    #[error("配置验证错误: {0}")]
    ValidationError(String),

    /// IO 错误
    #[error("IO 错误: {0}")]
    IoError(#[from] std::io::Error),
}

/// 配置验证特征
pub trait ConfigValidation {
    /// 验证配置是否合法
    fn validate_config(&self) -> Result<(), ConfigError>;
}

/// 通用配置加载器
pub struct ConfigLoader;

impl ConfigLoader {
    /// 验证配置文件
    pub fn validate_file(config_file: &str) -> Result<(), ConfigError> {
        let config_path = Path::new(config_file);
        if !config_path.exists() {
            return Err(ConfigError::FileNotFound(config_file.to_string()));
        }

        // 快速验证配置文件是否为空
        let metadata = fs::metadata(config_path)?;
        if metadata.len() == 0 {
            return Err(ConfigError::EmptyFile(config_file.to_string()));
        }

        // 验证配置文件中内容是否为空
        let content = fs::read_to_string(config_path)?;
        if content.trim().is_empty() {
            return Err(ConfigError::EmptyFile(config_file.to_string()));
        }

        Ok(())
    }

    /// 加载并验证配置
    pub fn load<T>(config_file: &str) -> Result<T, ConfigError>
    where
        T: DeserializeOwned + ConfigValidation + Validate,
    {
        Self::validate_file(config_file)?;

        let config = Config::builder()
            .add_source(File::with_name(config_file))
            .build()
            .map_err(ConfigError::ParseError)?;

        let settings: T = config
            .try_deserialize()
            .map_err(|e| ConfigError::ParseError(e))?;

        // 使用 validator 进行基础验证
        settings
            .validate()
            .map_err(|e| ConfigError::ValidationError(e.to_string()))?;

        // 执行自定义业务规则验证
        settings.validate_config()?;

        info!("从 {} 加载配置完成", config_file);

        Ok(settings)
    }
}
