use crate::basic::config::conf::{ConfigError, ConfigLoader, ConfigValidation};

use serde::Deserialize;
use validator::Validate;

/// 执行配置
#[derive(Debug, Default, Deserialize, Validate)]
pub struct Settings {
    /// 要执行的命令列表
    #[validate(length(min = 1, message = "命令列表不能为空"))]
    pub commands: Vec<String>,

    /// 是否启用并行执行
    pub parallel: bool,
}

impl ConfigValidation for Settings {
    fn validate_config(&self) -> Result<(), ConfigError> {
        // 这里仅保留跨字段验证或无法通过属性宏处理的验证 (可参考 orem 或 orebs 项目)
        Ok(())
    }
}

impl Settings {
    /// 创建新的配置实例
    pub fn new(config_file: &str) -> Result<Self, ConfigError> {
        ConfigLoader::load::<Self>(config_file)
    }
}
