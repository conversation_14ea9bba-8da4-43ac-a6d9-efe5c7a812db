use log::info;

use sysinfo::{CpuRefreshKind, MemoryRefreshKind, RefreshKind, System};
use tabled::{settings::Style, Table, Tabled};

/// 系统信息获取器
pub struct SysInfo {
    sys: System,
}

/// 表格数据行结构
#[derive(Tabled)]
struct InfoRow {
    #[tabled(rename = "Name")]
    name: String,
    #[tabled(rename = "Form")]
    value: String,
}

impl SysInfo {
    /// 创建新的系统信息获取器实例并初始化系统信息
    pub fn new() -> Self {
        let mut sys = System::new();
        // 使用 nothing() 创建 RefreshKind 后添加所需的刷新类型
        sys.refresh_specifics(
            RefreshKind::nothing()
                .with_cpu(CpuRefreshKind::everything())
                .with_memory(MemoryRefreshKind::everything()),
        );
        Self { sys }
    }

    /// 获取 CPU 信息
    fn get_cpu_info(&self) -> (String, usize, u64) {
        // 获取型号
        let brand = self
            .sys
            .cpus()
            .first()
            .map(|cpu| cpu.brand().to_string())
            .unwrap_or_else(|| "Unknown".to_string());
        // 获取物理核心数量
        let physical_core_count = self.sys.physical_core_count().unwrap_or(0);
        // 获取频率
        let frequency = self
            .sys
            .cpus()
            .first()
            .map(|cpu| cpu.frequency())
            .unwrap_or(0);
        (brand, physical_core_count, frequency)
    }

    /// 获取内存使用情况
    fn get_memory_usage(&self) -> (u64, u64, f32) {
        let total_memory = self.sys.total_memory() / 1024 / 1024; // 转换单位为 MB
        let used_memory = self.sys.used_memory() / 1024 / 1024; // 转换单位为 MB
        let memory_usage = if total_memory > 0 {
            (used_memory as f32 / total_memory as f32) * 100.0
        } else {
            0.0
        };
        (total_memory, used_memory, memory_usage)
    }

    /// 获取并打印系统和应用信息 (使用 tabled 库合并为一个表格)
    pub fn print_system_info(&self) {
        // 获取应用信息
        let app_name = env!("CARGO_PKG_NAME");
        let app_version = env!("CARGO_PKG_VERSION");

        // 获取系统信息
        let name = System::name().unwrap_or_else(|| "Unknown".to_string());
        let kernel_version = System::kernel_version().unwrap_or_else(|| "Unknown".to_string());
        let os_version = System::os_version().unwrap_or_else(|| "Unknown".to_string());
        let current_time = chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
        let (cpu_brand, cpu_cores, cpu_freq) = self.get_cpu_info();
        let (total_memory, used_memory, memory_usage) = self.get_memory_usage();

        // 合并应用信息和系统信息到一个表格
        let info_rows = vec![
            InfoRow {
                name: "App".to_string(),
                value: format!("🦀 {} {}", app_name, app_version),
            },
            InfoRow {
                name: "System".to_string(),
                value: format!("{} {} {}", name, os_version, kernel_version),
            },
            InfoRow {
                name: "CPU".to_string(),
                value: format!("{} {} {}MHz", cpu_cores, cpu_brand, cpu_freq),
            },
            InfoRow {
                name: "Memory".to_string(),
                value: format!(
                    "{}MB / {}MB ({}%)",
                    used_memory,
                    total_memory,
                    memory_usage.round() as i32
                ),
            },
            InfoRow {
                name: "Time".to_string(),
                value: current_time,
            },
        ];

        // 创建并格式化表格
        let mut table = Table::new(info_rows);
        table.with(Style::ascii_rounded()); // 定义表格风格, 参见: https://github.com/zhiburt/tabled

        // 逐行打印表格
        for line in table.to_string().lines() {
            info!("{}", line);
        }
    }
}
