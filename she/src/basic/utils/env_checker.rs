use nix::unistd::Uid;
use thiserror::Error;

/// 环境检查错误
#[derive(Error, Debug)]
pub enum EnvCheckError {
    #[error("需要 [root] 权限运行此应用")]
    NotRootUser,

    #[error("环境检查错误: {0}")]
    Other(String),
}

/// 环境检查配置
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct EnvCheckConfig {
    /// 是否检查 root 权限
    pub check_root: bool,
}

impl Default for EnvCheckConfig {
    fn default() -> Self {
        Self { check_root: true }
    }
}

/// 环境检查器
pub struct EnvChecker {
    config: EnvCheckConfig,
}

impl EnvChecker {
    /// 创建一个新的环境检查器
    pub fn new(config: EnvCheckConfig) -> Self {
        Self { config }
    }

    /// 执行所有环境检查
    pub fn check_all(&self) -> Result<(), EnvCheckError> {
        // 根据配置执行相应的检查
        if self.config.check_root {
            self.check_root()?;
        }

        Ok(())
    }

    /// 检查是否具有 root 权限
    fn check_root(&self) -> Result<(), EnvCheckError> {
        if !Uid::effective().is_root() {
            return Err(EnvCheckError::NotRootUser);
        }

        Ok(())
    }
}
