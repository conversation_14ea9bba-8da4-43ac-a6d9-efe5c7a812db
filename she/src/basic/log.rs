use flexi_logger::{
    style, Age, Cleanup, Criterion, DeferredNow, <PERSON>S<PERSON>, Logger, Naming, WriteMode,
};
use log::{Level, LevelFilter};
use once_cell::sync::Lazy;
use reqwest::Client;
use std::collections::HashSet;
use std::sync::atomic::{AtomicBool, Ordering};
use std::time::Duration;
use url::Url;

// 添加一个静态标志来防止告警循环
static ALERT_IN_PROGRESS: AtomicBool = AtomicBool::new(false);

// 告警配置结构体
#[derive(Clone)]
pub struct AlertConfig {
    pub enabled: bool,                // 是否启用告警
    pub alert_levels: HashSet<Level>, // 触发告警的日志级别
    pub api_url: String,              // 告警 API 基础 URL
    pub timeout: Duration,            // 请求超时时间
    pub retry_times: u32,             // 请求重试次数
    pub retry_interval: Duration,     // 请求重试间隔
}

// 日志配置结构体
pub struct LogConfig {
    pub log_file_name: &'static str, // 日志文件名称
    pub log_file_path: &'static str, // 日志文件路径
    pub console_output: bool,        // 是否启用控制台输出
    pub log_level: LevelFilter,      // 日志打印级别
    pub max_file_count: usize,       // 日志文件最大保留数量
    pub max_file_size: u64,          // 单个日志文件最大大小 (字节)
    pub show_file_and_line: bool,    // 是否显示文件和行号
    pub alert_config: AlertConfig,   // 告警配置
}

// 使用 Lazy 静态初始化日志配置
pub static LOG_CONFIG: Lazy<LogConfig> = Lazy::new(|| {
    let mut alert_levels = HashSet::new(); // 触发告警的日志级别
    alert_levels.insert(Level::Error);
    alert_levels.insert(Level::Warn);

    LogConfig {
        log_file_name: "she",             // 日志文件前缀命名
        log_file_path: "logs",            // 日志文件存储路径
        console_output: true,             // 是否启用控制台输出
        log_level: LevelFilter::Info,     // 打印的日志等级
        max_file_count: 15,               // 日志文件最大保留数量
        max_file_size: 100 * 1024 * 1024, // 每个日志文件最大 100 MB
        show_file_and_line: false,        // 是否打印具体的模块名称和行号
        alert_config: AlertConfig {
            enabled: true, // 是否启用告警
            alert_levels,  // 触发告警的日志级别
            api_url: "https://sctapi.ftqq.com/SCT145062TA-1gTkXtI4EFSM3jU9gvKyi7MB.send"
                .to_string(),
            timeout: Duration::from_secs(5),        // 5 秒超时
            retry_times: 3,                         // 重试 3 次
            retry_interval: Duration::from_secs(1), // 重试间隔时间 1 秒
        },
    }
});

// 发送告警的异步函数
async fn send_alert(
    level: Level,
    message: String,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let config = &LOG_CONFIG.alert_config;

    // 检查是否需要发送告警
    if !config.enabled || !config.alert_levels.contains(&level) {
        return Ok(());
    }

    // 检查是否已经在发送告警
    if ALERT_IN_PROGRESS.swap(true, Ordering::SeqCst) {
        return Ok(());
    }

    // 确保在函数结束时重置标志
    struct AlertGuard;
    impl Drop for AlertGuard {
        fn drop(&mut self) {
            ALERT_IN_PROGRESS.store(false, Ordering::SeqCst);
        }
    }
    let _guard = AlertGuard;

    // 构建请求 URL
    let mut url = Url::parse(&config.api_url)?;
    url.query_pairs_mut()
        .append_pair(
            "title",
            &format!(
                "{}-{}",
                LOG_CONFIG.log_file_name,
                level.to_string().to_lowercase()
            ),
        )
        .append_pair("desp", &message);

    let client = Client::new();
    let mut retry_count = 0;

    loop {
        match client
            .post(url.as_str())
            .header("Content-Type", "application/x-www-form-urlencoded")
            .timeout(config.timeout)
            .send()
            .await
        {
            Ok(response) if response.status().is_success() => {
                return Ok(());
            }
            _ if retry_count < config.retry_times => {
                retry_count += 1;
                tokio::time::sleep(config.retry_interval).await;
                continue;
            }
            Ok(response) => {
                return Err(format!("告警 API 返回了错误: {}", response.status()).into());
            }
            Err(e) => {
                return Err(format!("告警发送失败: {}", e).into());
            }
        }
    }
}

// 自定义日志格式化函数
fn custom_format(
    w: &mut dyn std::io::Write,
    now: &mut DeferredNow,
    record: &log::Record,
) -> Result<(), std::io::Error> {
    // 获取并清理日志消息
    let formatted_message = format!("{}", record.args()); // 临时 String 绑定到变量
    let message = formatted_message.trim(); // 获取切片 &str

    // 跳过空消息, 避免输出空行
    if message.is_empty() {
        return Ok(());
    }

    // 使用 style 函数来获取带颜色的日志级别
    let level_str = style(record.level()).paint(record.level().to_string());

    // 格式化并输出日志消息
    if LOG_CONFIG.show_file_and_line {
        let file = record.file().unwrap_or("unknown");
        let line_num = record.line().unwrap_or(0);
        write!(
            w,
            "[{}] {} [{}:{}] {}",
            level_str,
            now.format("%Y-%m-%d %H:%M:%S"),
            file,
            line_num,
            message
        )?;
    } else {
        write!(
            w,
            "[{}] {} {}",
            level_str,
            now.format("%Y-%m-%d %H:%M:%S"),
            message
        )?;
    }

    // 如果需要告警, 发送告警
    if LOG_CONFIG
        .alert_config
        .alert_levels
        .contains(&record.level())
    {
        let level = record.level();
        let alert_message = message.to_string(); // 将 &str 转换为 String

        tokio::spawn(async move {
            if let Err(e) = send_alert(level, alert_message).await {
                eprintln!("发送告警信息失败: {}", e);
            }
        });
    }

    Ok(())
}

// 初始化日志系统
pub fn init_logger() -> Result<(), flexi_logger::FlexiLoggerError> {
    let mut logger = Logger::try_with_str(&LOG_CONFIG.log_level.to_string())?
        .format(custom_format)
        .log_to_file(
            FileSpec::default()
                .directory(LOG_CONFIG.log_file_path)
                .basename(LOG_CONFIG.log_file_name)
                .suffix("log"),
        )
        .write_mode(WriteMode::Direct) // 写入模式: 每行日志直接写入输出, 无需缓冲不需要额外的线程 (适用于实时查看日志)
        .rotate(
            Criterion::AgeOrSize(Age::Day, LOG_CONFIG.max_file_size), // 每天和按照单个日志文件最大大小轮转
            Naming::Timestamps,
            Cleanup::KeepLogFiles(LOG_CONFIG.max_file_count), // 启动时按照日志文件最大保留数量自动清理较旧的日志文件
        );

    if LOG_CONFIG.console_output {
        logger = logger.duplicate_to_stderr(flexi_logger::Duplicate::All);
    }

    match logger.start() {
        Ok(_) => {
            log::debug!("日志初始化成功");
            Ok(())
        }
        Err(e) => {
            eprintln!("日志初始化失败: {}", e);
            Err(e)
        }
    }
}
