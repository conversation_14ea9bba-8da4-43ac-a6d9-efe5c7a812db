### Run ###
```bash
mkdir -p /usr/local/zhuro && mkdir -p /usr/local/zhuro/she && cd /usr/local/zhuro/she && curl -# -O https://mirror.lilh.net/priv/ZhuRO/she.tar.gz && tar xzf she.tar.gz && rm -rf she.tar.gz
```
```bash
./she
```

### Update ###
```bash
cd ~ && curl -# -O https://mirror.lilh.net/priv/ZhuRO/she.tar.gz && tar xzf she.tar.gz && rm -rf she.tar.gz config.toml /usr/local/zhuro/she/she && mv she /usr/local/zhuro/she/ && ls && cd /usr/local/zhuro/she && ls
```

### Build ###
```bash
cross build --target x86_64-unknown-linux-musl --release && cd target/x86_64-unknown-linux-musl/release && cp -a ../../../config.toml . && tar -czvf she.tar.gz config.toml she && mv *.tar.gz ~
```

### Cron ###
#### 每小时 20 分执行 ####
```bash
20 * * * * cd /usr/local/zhuro/she && ./she
```